# Markdown Word Counter MCP - 使用指南

## 项目概述

这个项目将原有的Python Markdown字数统计工具转换为了MCP (Model Context Protocol) 服务器，提供了两种使用方式：

1. **Python版本** (`counter.py`) - 独立命令行工具
2. **MCP服务器版本** (`build/index.js`) - 可集成到支持MCP的AI客户端中

## 功能特性

- ✅ 支持中文字符和英文单词的准确统计
- ✅ 智能处理Markdown语法（标题、列表、链接等）
- ✅ 移除HTML标签
- ✅ 提供基础和详细两种统计模式
- ✅ 完全兼容MCP协议

## 安装和构建

```bash
# 安装依赖
npm install

# 构建TypeScript代码
npm run build
```

## 使用方法

### 1. Python版本（独立使用）

```bash
python counter.py test.md
# 输出: Total Words: 1184
```

### 2. MCP服务器版本

#### 配置MCP客户端

在你的MCP客户端配置文件中添加：

```json
{
  "mcpServers": {
    "markdown-word-counter": {
      "command": "node",
      "args": [
        "d:/11_WorkSpace/markdown-word-counter-mcp/build/index.js"
      ]
    }
  }
}
```

#### 可用工具

**1. count_words - 基础字数统计**
```json
{
  "name": "count_words",
  "arguments": {
    "file_path": "test.md"
  }
}
```

**2. detailed_word_count - 详细统计**
```json
{
  "name": "detailed_word_count", 
  "arguments": {
    "file_path": "test.md"
  }
}
```

## 测试结果

使用提供的`test.md`文件测试：

```
Total Words: 1184
Chinese Characters: 1170
English Words: 14
```

## 统计逻辑

1. **移除HTML标签**: `<.*?>`
2. **处理Markdown链接**: 保留链接文字，移除URL
3. **移除Markdown标记**: 标题符号(`#`)、列表符号(`-*+`)
4. **规范化空白字符**: 多个空格合并为单个空格
5. **分别统计**:
   - 中文字符: `[\u4e00-\u9fa5]`
   - 英文单词: `\b\w+\b` (先移除中文字符避免重复计算)
6. **合计**: 中文字符数 + 英文单词数

## 项目结构

```
markdown-word-counter-mcp/
├── src/
│   ├── counter.ts          # 核心统计逻辑
│   └── index.ts           # MCP服务器入口
├── build/                 # 编译后的JavaScript文件
├── counter.py            # Python版本
├── test.md              # 测试文件
├── package.json         # 项目配置
├── tsconfig.json        # TypeScript配置
└── README.md           # 项目说明
```

## 开发和调试

```bash
# 开发模式（监听文件变化）
npm run dev

# 直接运行服务器
npm start

# 测试MCP功能
node test-mcp.js
node test-detailed.js
```

## 注意事项

1. 确保Node.js版本 >= 18
2. 文件路径使用绝对路径或相对于工作目录的路径
3. 支持UTF-8编码的Markdown文件
4. MCP服务器通过stdio进行通信，适合集成到AI客户端中

## 故障排除

如果遇到问题：

1. 检查Node.js和npm版本
2. 确认文件路径正确
3. 查看服务器错误输出（stderr）
4. 使用调试脚本验证功能
