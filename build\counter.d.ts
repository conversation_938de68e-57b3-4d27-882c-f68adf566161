/**
 * Count words in a Markdown file, supporting both Chinese characters and English words
 * @param filePath Path to the Markdown file
 * @returns Total word count
 */
export declare function countMarkdownWords(filePath: string): Promise<number>;
/**
 * Get detailed word count statistics
 * @param filePath Path to the Markdown file
 * @returns Detailed statistics object
 */
export declare function getDetailedWordCount(filePath: string): Promise<{
    totalWords: number;
    chineseChars: number;
    englishWords: number;
    filePath: string;
}>;
//# sourceMappingURL=counter.d.ts.map