#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema, } from '@modelcontextprotocol/sdk/types.js';
import { countMarkdownWords, getDetailedWordCount } from './counter.js';
class MarkdownWordCounterServer {
    server;
    constructor() {
        this.server = new Server({
            name: 'markdown-word-counter',
            version: '1.0.0',
        }, {
            capabilities: {
                tools: {},
            },
        });
        this.setupToolHandlers();
        this.setupErrorHandling();
    }
    setupErrorHandling() {
        this.server.onerror = (error) => {
            console.error('[MCP Error]', error);
        };
        process.on('SIGINT', async () => {
            await this.server.close();
            process.exit(0);
        });
    }
    setupToolHandlers() {
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            return {
                tools: [
                    {
                        name: 'count_words',
                        description: 'Count total words in a Markdown file (supports Chinese characters and English words)',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                file_path: {
                                    type: 'string',
                                    description: 'Path to the Markdown file to analyze',
                                },
                            },
                            required: ['file_path'],
                        },
                    },
                    {
                        name: 'detailed_word_count',
                        description: 'Get detailed word count statistics for a Markdown file',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                file_path: {
                                    type: 'string',
                                    description: 'Path to the Markdown file to analyze',
                                },
                            },
                            required: ['file_path'],
                        },
                    },
                ],
            };
        });
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            try {
                if (name === 'count_words') {
                    const { file_path } = args;
                    const wordCount = await countMarkdownWords(file_path);
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `Total Words: ${wordCount}`,
                            },
                        ],
                    };
                }
                if (name === 'detailed_word_count') {
                    const { file_path } = args;
                    const stats = await getDetailedWordCount(file_path);
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `Detailed Word Count for: ${stats.filePath}
Total Words: ${stats.totalWords}
Chinese Characters: ${stats.chineseChars}
English Words: ${stats.englishWords}`,
                            },
                        ],
                    };
                }
                throw new Error(`Unknown tool: ${name}`);
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Error: ${errorMessage}`,
                        },
                    ],
                    isError: true,
                };
            }
        });
    }
    async run() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('Markdown Word Counter MCP server running on stdio');
    }
}
const server = new MarkdownWordCounterServer();
server.run().catch(console.error);
//# sourceMappingURL=index.js.map