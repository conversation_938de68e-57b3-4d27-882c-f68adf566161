import re
import sys

def count_markdown_words(file_path):
    # 定义匹配规则
    html_tag_pattern = r'<.*?>'        # HTML标签
    markdown_link_pattern = r'\[([^\]]+)\]\([^)]+\)'  # Markdown链接（保留文字）
    markdown_header_pattern = r'#+\s*' # 标题标记
    markdown_list_pattern = r'[-*+]\s' # 列表标记
    punctuation_pattern = r'[!\"#$%&\'()*+,\-./:;<=>?@$$\\$$^_`{|}~]'  # 标点符号

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 移除HTML标签
            content = re.sub(html_tag_pattern, '', content)
            
            # 处理链接（保留链接文字）
            content = re.sub(markdown_link_pattern, r'\1', content)
            
            # 移除Markdown标记
            content = re.sub(markdown_header_pattern, '', content)
            content = re.sub(markdown_list_pattern, '', content)
            
            # 移除多余的空格和换行
            content = re.sub(r'\s+', ' ', content).strip()
            
            # 移除标点符号（可选，根据需求）
            # content = re.sub(punctuation_pattern, '', content)
            
            # 统计字数（包括中文字符和英文单词）
            chinese_chars = re.findall(r'[\u4e00-\u9fa5]', content)
            # 先移除中文字符，再统计英文单词，避免重复计算
            content_without_chinese = re.sub(r'[\u4e00-\u9fa5]', '', content)
            english_words = re.findall(r'\b\w+\b', content_without_chinese)

            total_words = len(chinese_chars) + len(english_words)
            
            return total_words

    except Exception as e:
        print(f"错误: {e}")
        return 0

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python counter.py <markdown文件路径>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    word_count = count_markdown_words(file_path)
    print(f"Total Words: {word_count}")