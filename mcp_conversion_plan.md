# Markdown Word Counter MCP Conversion Plan

## Overview
This document outlines the plan to convert the existing Python Markdown word counter (`counter.py`) into an MCP server.

## Implementation Steps

### 1. Create MCP Server Project
```bash
cd C:\Users\<USER>\AppData\Roaming\Roo-Code\MCP
npx @modelcontextprotocol/create-server markdown-word-counter
cd markdown-word-counter
npm install
```

### 2. Implement Word Count Tool
**File: `src/counter.ts`**
```typescript
import fs from 'fs/promises';

export async function countMarkdownWords(filePath: string): Promise<number> {
    const content = await fs.readFile(filePath, 'utf-8');
    
    // Remove HTML tags
    let cleaned = content.replace(/<.*?>/g, '');
    
    // Process Markdown links (keep link text)
    cleaned = cleaned.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');
    
    // Remove Markdown headers and lists
    cleaned = cleaned.replace(/#+\s*/g, '')
                     .replace(/[-*+]\s/g, '');
    
    // Normalize whitespace
    cleaned = cleaned.replace(/\s+/g, ' ').trim();
    
    // Count Chinese characters and English words
    const chineseChars = (cleaned.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = (cleaned.match(/\b\w+\b/g) || []).length;
    
    return chineseChars + englishWords;
}
```

### 3. Create Server Entrypoint
**File: `src/index.ts`**
```typescript
import { McpServer, StdioServerTransport } from "@modelcontextprotocol/sdk";
import { z } from "zod";
import { countMarkdownWords } from './counter';

const server = new McpServer({
  name: "markdown-word-counter",
  version: "1.0.0"
});

server.tool(
  "count_words",
  {
    file_path: z.string().describe("Path to Markdown file")
  },
  async ({ file_path }) => {
    try {
      const wordCount = await countMarkdownWords(file_path);
      return {
        content: [{
          type: "text",
          text: `Total Words: ${wordCount}`
        }]
      };
    } catch (error: any) {
      return {
        content: [{
          type: "text",
          text: `Error: ${error.message}`
        }],
        isError: true
      };
    }
  }
);

const transport = new StdioServerTransport();
await server.connect(transport);
console.error('Markdown Word Counter MCP server running');
```

### 4. Build Executable
```bash
npm run build
```

### 5. Configure MCP Settings
Add to `mcp_settings.json`:
```json
{
  "mcpServers": {
    "markdown-counter": {
      "command": "node",
      "args": [
        "C:/Users/<USER>/AppData/Roaming/Roo-Code/MCP/markdown-word-counter/build/index.js"
      ]
    }
  }
}
```

### 6. Usage Example
Once configured, you can use the tool:
```bash
# Via CLI
roo mcp markdown-counter count_words --file_path "document.md"

# Programmatically
<use_mcp_tool>
<server_name>markdown-counter</server_name>
<tool_name>count_words</tool_name>
<arguments>
{"file_path": "document.md"}
</arguments>
</use_mcp_tool>
```

## Key Improvements
1. **Type Safety**: TypeScript implementation
2. **Modular Design**: Separated counter logic from server setup
3. **Better Regex**: Fixed Markdown link pattern (`\[([^\]]+)\]\([^)]+\)`)
4. **Async Handling**: Proper Promise-based file operations
5. **Error Handling**: Structured error responses