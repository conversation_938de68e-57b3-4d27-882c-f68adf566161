import { readFile } from 'fs/promises';

/**
 * Count words in a Markdown file, supporting both Chinese characters and English words
 * @param filePath Path to the Markdown file
 * @returns Total word count
 */
export async function countMarkdownWords(filePath: string): Promise<number> {
    try {
        const content = await readFile(filePath, 'utf-8');
        
        // Define regex patterns
        const htmlTagPattern = /<.*?>/g;                    // HTML tags
        const markdownLinkPattern = /\[([^\]]+)\]\([^)]+\)/g; // Markdown links (keep text)
        const markdownHeaderPattern = /#+\s*/g;             // Header markers
        const markdownListPattern = /[-*+]\s/g;             // List markers
        
        let cleaned = content;
        
        // Remove HTML tags
        cleaned = cleaned.replace(htmlTagPattern, '');
        
        // Process Markdown links (keep link text only)
        cleaned = cleaned.replace(markdownLinkPattern, '$1');
        
        // Remove Markdown markers
        cleaned = cleaned.replace(markdownHeaderPattern, '');
        cleaned = cleaned.replace(markdownListPattern, '');
        
        // Normalize whitespace
        cleaned = cleaned.replace(/\s+/g, ' ').trim();
        
        // Count Chinese characters and English words
        const chineseChars = (cleaned.match(/[\u4e00-\u9fa5]/g) || []).length;
        // Remove Chinese characters first to avoid double counting
        const cleanedWithoutChinese = cleaned.replace(/[\u4e00-\u9fa5]/g, '');
        const englishWords = (cleanedWithoutChinese.match(/\b\w+\b/g) || []).length;
        
        return chineseChars + englishWords;
        
    } catch (error) {
        throw new Error(`Failed to count words in ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Get detailed word count statistics
 * @param filePath Path to the Markdown file
 * @returns Detailed statistics object
 */
export async function getDetailedWordCount(filePath: string): Promise<{
    totalWords: number;
    chineseChars: number;
    englishWords: number;
    filePath: string;
}> {
    try {
        const content = await readFile(filePath, 'utf-8');
        
        // Apply same cleaning logic
        let cleaned = content;
        cleaned = cleaned.replace(/<.*?>/g, '');
        cleaned = cleaned.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');
        cleaned = cleaned.replace(/#+\s*/g, '');
        cleaned = cleaned.replace(/[-*+]\s/g, '');
        cleaned = cleaned.replace(/\s+/g, ' ').trim();
        
        const chineseChars = (cleaned.match(/[\u4e00-\u9fa5]/g) || []).length;
        // Remove Chinese characters first to avoid double counting
        const cleanedWithoutChinese = cleaned.replace(/[\u4e00-\u9fa5]/g, '');
        const englishWords = (cleanedWithoutChinese.match(/\b\w+\b/g) || []).length;
        const totalWords = chineseChars + englishWords;
        
        return {
            totalWords,
            chineseChars,
            englishWords,
            filePath
        };
        
    } catch (error) {
        throw new Error(`Failed to get detailed word count for ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
}
