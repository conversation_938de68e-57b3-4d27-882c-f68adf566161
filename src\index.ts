#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { countMarkdownWords, getDetailedWordCount, WordCountOptions } from './counter.js';

class MarkdownWordCounterServer {
  private server: Server;

  constructor() {
    this.server = new Server(
      {
        name: 'markdown-word-counter',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  private setupErrorHandling(): void {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers(): void {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'count_words',
            description: 'Count total words in a Markdown file (supports Chinese characters and English words)',
            inputSchema: {
              type: 'object',
              properties: {
                file_path: {
                  type: 'string',
                  description: 'Path to the Markdown file to analyze',
                },
                remove_html_tags: {
                  type: 'boolean',
                  description: 'Whether to remove HTML tags (default: true)',
                  default: true,
                },
                process_markdown_links: {
                  type: 'boolean',
                  description: 'Whether to process Markdown links (default: true)',
                  default: true,
                },
                remove_markdown_headers: {
                  type: 'boolean',
                  description: 'Whether to remove Markdown headers (default: true)',
                  default: true,
                },
                remove_markdown_lists: {
                  type: 'boolean',
                  description: 'Whether to remove Markdown list markers (default: true)',
                  default: true,
                },
                normalize_whitespace: {
                  type: 'boolean',
                  description: 'Whether to normalize whitespace (default: true)',
                  default: true,
                },
              },
              required: ['file_path'],
            },
          },
          {
            name: 'detailed_word_count',
            description: 'Get detailed word count statistics for a Markdown file',
            inputSchema: {
              type: 'object',
              properties: {
                file_path: {
                  type: 'string',
                  description: 'Path to the Markdown file to analyze',
                },
                remove_html_tags: {
                  type: 'boolean',
                  description: 'Whether to remove HTML tags (default: true)',
                  default: true,
                },
                process_markdown_links: {
                  type: 'boolean',
                  description: 'Whether to process Markdown links (default: true)',
                  default: true,
                },
                remove_markdown_headers: {
                  type: 'boolean',
                  description: 'Whether to remove Markdown headers (default: true)',
                  default: true,
                },
                remove_markdown_lists: {
                  type: 'boolean',
                  description: 'Whether to remove Markdown list markers (default: true)',
                  default: true,
                },
                normalize_whitespace: {
                  type: 'boolean',
                  description: 'Whether to normalize whitespace (default: true)',
                  default: true,
                },
              },
              required: ['file_path'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        if (name === 'count_words') {
          const {
            file_path,
            remove_html_tags,
            process_markdown_links,
            remove_markdown_headers,
            remove_markdown_lists,
            normalize_whitespace
          } = args as {
            file_path: string;
            remove_html_tags?: boolean;
            process_markdown_links?: boolean;
            remove_markdown_headers?: boolean;
            remove_markdown_lists?: boolean;
            normalize_whitespace?: boolean;
          };

          const options: WordCountOptions = {
            removeHtmlTags: remove_html_tags,
            processMarkdownLinks: process_markdown_links,
            removeMarkdownHeaders: remove_markdown_headers,
            removeMarkdownLists: remove_markdown_lists,
            normalizeWhitespace: normalize_whitespace
          };

          const wordCount = await countMarkdownWords(file_path, options);

          return {
            content: [
              {
                type: 'text',
                text: `Total Words: ${wordCount}`,
              },
            ],
          };
        }

        if (name === 'detailed_word_count') {
          const {
            file_path,
            remove_html_tags,
            process_markdown_links,
            remove_markdown_headers,
            remove_markdown_lists,
            normalize_whitespace
          } = args as {
            file_path: string;
            remove_html_tags?: boolean;
            process_markdown_links?: boolean;
            remove_markdown_headers?: boolean;
            remove_markdown_lists?: boolean;
            normalize_whitespace?: boolean;
          };

          const options: WordCountOptions = {
            removeHtmlTags: remove_html_tags,
            processMarkdownLinks: process_markdown_links,
            removeMarkdownHeaders: remove_markdown_headers,
            removeMarkdownLists: remove_markdown_lists,
            normalizeWhitespace: normalize_whitespace
          };

          const stats = await getDetailedWordCount(file_path, options);

          return {
            content: [
              {
                type: 'text',
                text: `Detailed Word Count for: ${stats.filePath}
Total Words: ${stats.totalWords}
Chinese Characters: ${stats.chineseChars}
English Words: ${stats.englishWords}

Processing Options:
- Remove HTML Tags: ${stats.options.removeHtmlTags ?? true}
- Process Markdown Links: ${stats.options.processMarkdownLinks ?? true}
- Remove Markdown Headers: ${stats.options.removeMarkdownHeaders ?? true}
- Remove Markdown Lists: ${stats.options.removeMarkdownLists ?? true}
- Normalize Whitespace: ${stats.options.normalizeWhitespace ?? true}`,
              },
            ],
          };
        }

        throw new Error(`Unknown tool: ${name}`);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${errorMessage}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  async run(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Markdown Word Counter MCP server running on stdio');
  }
}

const server = new MarkdownWordCounterServer();
server.run().catch(console.error);
